import {styled} from 'styled-components';
import { fontSize, fontWeight } from '@obiemoney/theme/style.typography.ts';
import { brand, colors, greyScaleColour } from '@obiemoney/theme/style.palette.ts';

export const StyledSearchInput = styled.input`
  padding: 14px 0px 12px 16px;
  width: 100%;
  border: none;
  outline: none;
  font-size: ${fontSize.b1};
  font-weight: ${fontWeight.regular};
  line-height: 20px;
  border-radius: 10px;
  color: ${brand.black};
  &::placeholder {
    color: ${greyScaleColour.grey100};
  }
`;

export const StyledActionItem = styled.div<{ lastItem?: boolean }>`
    display: flex;
    white-space: nowrap;
    width: 100%;
`;

export const SearchInputContainer = styled.div`
    display: flex;
    width: 100%;
    align-items: center;
    border: 1px solid #D0D0D2;
    border-radius: 4px;
    &:focus-within{
        border-color: ${brand.primaryMain};
    }

`;