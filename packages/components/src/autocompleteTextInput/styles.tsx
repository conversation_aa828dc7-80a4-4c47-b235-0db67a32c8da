import { styled } from 'styled-components';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import { brand, colors, greyScaleColour } from '@obiemoney/theme/style.palette.ts';
import { fontSize, fontWeight } from '@obiemoney/theme/style.typography.ts';
import { Popper } from '@mui/material';

export const StyledChipContainer = styled.div`
  display: flex;
  padding: 2px 5px 2px 10px;
  justify-content: center;
  align-items: center;
  gap: 5px;
  border-radius: 60px;
  background: ${brand.primaryMain};
  margin-right: 5px;
  margin-bottom: 5px;
`;

export const StyledChipLabel = styled.span`
  color: ${brand.white};
  // font-size: ${fontSize.b2};
  // font-weight: ${fontWeight.medium};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 72px;
`;

export const StyledChipCloseContainer = styled.div`
  cursor: pointer;
  display: flex;
`;

export const StyledCanceledIcon = styled(CancelOutlinedIcon)`
  width: 18px;
  color: ${brand.white};
`;


export const StyledAutocompletePopper = styled(Popper)`
  z-index: 1300;

  .MuiAutocomplete-listbox {
    padding: 0;
    max-height: 300px;
    overflow: auto;
  }

  .MuiAutocomplete-option {
    font-size: 14px;
    line-height:21px;
    height: 44px;
    padding: 10px 14px;
    border-bottom: 0.3px solid ${greyScaleColour.grey10};
    cursor: pointer;
    color: ${brand.black}; /* Default text color */

    /* Hover styling */
    &:hover {
      background-color: ${greyScaleColour.grey10};
      color: ${brand.black};
    }

    /* Focus styling to handle keyboard selection */
    &[data-focus="true"] {
      background-color: ${greyScaleColour.grey10};
      color: ${brand.white};
    }

    /* Ensure focus state styling if hover is absent */
    &.Mui-focused:not(:hover) {
      background-color: ${greyScaleColour.grey10};
      color: ${brand.black};
    }
  }
`;
