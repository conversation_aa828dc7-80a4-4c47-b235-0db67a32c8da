{"name": "@obiemoney/redux-global", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@babel/runtime": "^7.27.6", "@obiemoney/messages": "workspace:*", "@obiemoney/models": "workspace:*", "@obiemoney/utils": "workspace:*", "connected-react-router": "^6.9.3", "history": "^4.10.1", "react": "^19.1.0", "redux": "^5.0.1", "redux-saga": "^1.3.0"}, "devDependencies": {"@types/react": "^19.1.8"}}