{"name": "performance-health-lab", "version": "1.0.0", "description": "Performance Health Lab Monorepo", "main": "index.js", "private": true, "scripts": {"build": "pnpm --recursive build", "build:packages": "pnpm --filter \"./packages/**\" build", "dev": "pnpm --recursive --parallel dev", "dev:client": "pnpm --filter client dev", "dev:server": "pnpm --filter server dev", "lint": "pnpm --recursive lint", "lint:fix": "pnpm --recursive lint --fix", "test": "pnpm --recursive test", "clean": "pnpm --recursive clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "pnpm --recursive exec tsc --noEmit", "install:all": "pnpm install", "clean:all": "pnpm --recursive exec rm -rf node_modules && rm -rf node_modules && rm -rf pnpm-lock.yaml"}, "keywords": ["monorepo"], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "devDependencies": {"@mono/eslint-config": "workspace:*", "@mono/typescript-config": "workspace:*", "eslint": "^9.30.1", "prettier": "^3.6.2", "typescript": "^5.8.3"}}