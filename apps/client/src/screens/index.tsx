// components/screens/Screens.tsx
import React from 'react';
import { Switch, Route, Redirect } from 'react-router-dom';
import OnlyWith from '../onlyWith';
import { AuthenticationStatus } from '@obiemoney/redux-global/src/reducers';
import { Right } from '@obiemoney/redux-global/src/reducers/auth';
import { Login } from './auth';
import { routes } from '../myUtils';
import Dashboard from './dashboard';
import { isApplicableFeatureLevel } from '../config';
import { Helmet } from "react-helmet";
const redirectToRoot = () => <Redirect to={routes.dashboard.root} />;
const redirectToLogin = () => <Redirect to={routes.login} />;

const modules = [
  {
    key: 'dashboard',
    right: Right.DASHBOARD,
    route: routes.dashboard.root,
    component: Dashboard,
  }
];

const Screens: React.FC = () => (
  <>   
    <Helmet>
        <link rel="icon" type="image/png" href="../assets/images/favicon.png" />
      </Helmet>
    <OnlyWith
      status={AuthenticationStatus.AUTHENTICATED}
      isApplicableFeatureLevel={isApplicableFeatureLevel}
    >
      <Switch>
        {/* {modules.map((module) => (
          <Route key={module.key} path={module.route} component={module.component} />
        ))} */}
        <Route component={redirectToRoot} />
      </Switch>
    </OnlyWith>
    <OnlyWith status={AuthenticationStatus.NOT_AUTHENTICATED}>
      <Switch>
        <Route path={routes.login} component={Login} />
        {modules.map((module) => (
          <Route key={module.key} path={module.route} component={module.component} />
        ))}
        <Route component={redirectToLogin} />
      </Switch>
    </OnlyWith>
  </>
);

export default Screens;
