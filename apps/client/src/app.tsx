import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@obiemoney/theme';
import { GlobalStyle } from '@obiemoney/theme/style.global';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { Slide, ToastContainer } from 'react-toastify';
import store, { history } from './redux/store';
// import { fetchBaseData } from './redux/actions';
import 'react-toastify/dist/ReactToastify.css';
import Screen from './screens';
import { Loader } from '@obiemoney/components';

// store.dispatch(fetchBaseData());

function App() {
  return (
    <Provider store={store}>
      <LocalizationProvider dateAdapter={AdapterMoment}>
        <ConnectedRouter history={history}>
          <React.StrictMode>
            <GlobalStyle />
            <ThemeProvider theme={theme}>
              <Screen />
            </ThemeProvider>
            <ToastContainer
              hideProgressBar
              position="top-right"
              closeButton={false}
              autoClose={3000}
              draggable={false}
              transition={Slide}
            />
            <Loader />
          </React.StrictMode>
        </ConnectedRouter>
      </LocalizationProvider>
    </Provider>
  );
}

export default App;
